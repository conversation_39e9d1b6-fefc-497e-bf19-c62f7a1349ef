2025-05-31 03:33:35,085 - INFO - 🚀 Fixed FastLLaMA Training - Phase 1: Basic Training with Advanced Features
2025-05-31 03:33:35,085 - INFO - Device: cuda
2025-05-31 03:33:35,227 - INFO - ✅ Tokenizer loaded. Vocab size: 64811
2025-05-31 03:33:35,227 - INFO - 🧠 Creating FastLLaMA model with FIXED advanced features...
2025-05-31 03:33:38,653 - INFO - ✅ Model created: 208.7M total parameters
2025-05-31 03:33:38,653 - INFO - ✅ Trainable parameters: 208.7M (100.0%)
2025-05-31 03:33:38,653 - INFO - 🔍 Validating gradient flow...
2025-05-31 03:33:38,654 - INFO - ✅ Parameters requiring gradients: 172/172
2025-05-31 03:33:38,654 - INFO - 🔍 Checking advanced features...
2025-05-31 03:33:38,655 - INFO - ✅ Hierarchical attention layers: 2
2025-05-31 03:33:38,655 - INFO - ✅ Attention weights parameters: 6
2025-05-31 03:33:38,655 - INFO - ✅ Context compression enabled: True
2025-05-31 03:33:38,655 - INFO - ✅ Early exit heads: 3
2025-05-31 03:33:38,655 - INFO - ✅ KV quantizers: 6
2025-05-31 03:33:38,656 - INFO - 🧪 Testing forward pass with gradients...
2025-05-31 03:33:40,371 - INFO - ✅ Parameters with gradients: 102/172
2025-05-31 03:33:40,375 - INFO - ✅ Forward pass successful. Total Loss: 11.0716
2025-05-31 03:33:40,375 - WARNING - ⚠️ Parameters without gradients: 70
2025-05-31 03:33:40,375 - WARNING -     - layers.0.self_attn.k_quantizer
2025-05-31 03:33:40,375 - WARNING -     - layers.0.self_attn.v_quantizer
2025-05-31 03:33:40,375 - WARNING -     - layers.0.self_attn.group_adapter.weight
2025-05-31 03:33:40,376 - WARNING -     - layers.0.self_attn.group_adapter.bias
2025-05-31 03:33:40,376 - WARNING -     - layers.1.self_attn.k_quantizer
2025-05-31 03:33:40,376 - WARNING -     ... and 65 more
2025-05-31 03:33:40,376 - ERROR - ❌ Forward pass test failed!
