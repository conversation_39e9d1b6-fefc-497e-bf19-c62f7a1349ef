"""
Simple test for FastLLaMA Web UI
"""

import gradio as gr
import torch
import os
import sys
from pathlib import Path

# Add FastLLaMA to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_function(text):
    """Simple test function."""
    return f"You entered: {text}"

def create_simple_ui():
    """Create a simple test UI."""
    
    with gr.<PERSON>(title="FastLLaMA Test UI") as demo:
        gr.Markdown("# 🚀 FastLLaMA Test UI")
        
        with gr.Row():
            input_text = gr.Textbox(label="Test Input", value="Hello World")
            output_text = gr.Textbox(label="Test Output")
        
        test_btn = gr.<PERSON><PERSON>("Test")
        
        test_btn.click(
            fn=test_function,
            inputs=[input_text],
            outputs=[output_text]
        )
    
    return demo

if __name__ == "__main__":
    print("🚀 Starting FastLLaMA Test UI...")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    print(f"Gradio version: {gr.__version__}")
    
    demo = create_simple_ui()
    demo.launch(
        server_name="127.0.0.1",
        server_port=7860,
        share=False,
        debug=True
    )
