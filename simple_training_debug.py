"""
Simple FastLLaMA Training Debug Script

This script uses a basic PyTorch training loop to debug training issues
and ensure the model can actually learn without complex trainer interference.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import logging
import time
from pathlib import Path
from datetime import datetime

# Add FastLLaMA to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastllama import FastLLaMAConfig, FastLLaMAModel
from fastllama.data import DataConfig, FastLLaMADataLoader
from transformers import AutoTokenizer

def setup_logging():
    """Setup basic logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )
    return logging.getLogger(__name__)

def create_simple_model_config(vocab_size):
    """Create a very simple model for debugging."""
    return FastLLaMAConfig(
        # Very small model for fast debugging
        hidden_size=256,
        intermediate_size=256*4,
        num_attention_heads=4,
        num_key_value_heads=4,
        num_hidden_layers=4,
        vocab_size=vocab_size + 8,
        max_position_embeddings=1024,

        # Disable ALL advanced features
        enable_context_compression=False,
        enable_early_exit=False,
        use_flash_attention=False,
        use_kernel_fusion=False,
        use_gradient_checkpointing=False,
        kv_cache_quantization=False,
        parameter_sharing=False,

        # Simple attention only
        local_layers=[],
        sparse_layers=[],
        hierarchical_layers=[],
        full_attention_layers=list(range(4)),
        early_exit_layers=[],

        # Basic settings
        dynamic_batching=False,
        memory_aware_batching=False,
    )

def create_simple_data_config():
    """Create simple data configuration."""
    return DataConfig(
        dataset_name="HuggingFaceTB/smollm-corpus",
        dataset_config="fineweb-edu-dedup",
        tokenizer_name="D:\\vlinhd11\\Projects\\LCMS_LLM\\MOE-LLM\\v2\\my_tokenizer\\llama_en_vi_tokenizer",
        max_length=256,  # Very short for debugging
        batch_size=2,    # Very small batch
        streaming=True,
        num_workers=0,   # No multiprocessing
        min_length=200,
        padding="max_length",
        truncation=True,
        filter_empty=True,
        pin_memory=False,
        prefetch_factor=None,  # Must be None when num_workers=0
        drop_last=True,
    )

def test_model_forward(model, tokenizer, device, logger):
    """Test basic model forward pass."""
    logger.info("Testing model forward pass...")

    # Create dummy input
    batch_size = 2
    seq_length = 256
    input_ids = torch.randint(0, tokenizer.vocab_size, (batch_size, seq_length)).to(device)
    labels = input_ids.clone()

    model.eval()
    with torch.no_grad():
        outputs = model(input_ids=input_ids, labels=labels)

    logger.info(f"Forward pass successful!")
    logger.info(f"Loss: {outputs['loss'].item():.4f}")
    logger.info(f"Logits shape: {outputs['logits'].shape}")

    # Check if loss is reasonable
    expected_loss = torch.log(torch.tensor(tokenizer.vocab_size, dtype=torch.float))
    logger.info(f"Expected random loss: {expected_loss.item():.4f}")

    return outputs['loss'].item()

def simple_training_loop(model, dataloader, optimizer, device, logger, max_steps=100):
    """Simple training loop without complex features."""
    model.train()

    total_loss = 0
    step = 0
    start_time = time.time()

    logger.info(f"Starting simple training loop for {max_steps} steps...")

    for batch in dataloader:
        if step >= max_steps:
            break

        # Move batch to device
        input_ids = batch['input_ids'].to(device)
        labels = batch['labels'].to(device)
        attention_mask = batch.get('attention_mask', None)
        if attention_mask is not None:
            attention_mask = attention_mask.to(device)

        # Forward pass
        optimizer.zero_grad()
        outputs = model(
            input_ids=input_ids,
            labels=labels,
            attention_mask=attention_mask
        )

        loss = outputs['loss']

        # Backward pass
        loss.backward()

        # Gradient clipping
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)

        # Optimizer step
        optimizer.step()

        total_loss += loss.item()
        step += 1

        # Log every 10 steps
        if step % 10 == 0:
            avg_loss = total_loss / step
            elapsed = time.time() - start_time
            speed = step / elapsed

            logger.info(f"Step {step}: loss={loss.item():.4f}, avg_loss={avg_loss:.4f}, speed={speed:.2f} it/s")

            # Check for improvement
            if step > 20 and avg_loss > 10.0:
                logger.warning("Loss not decreasing - potential training issue!")

    final_avg_loss = total_loss / step if step > 0 else float('inf')
    logger.info(f"Training completed. Final average loss: {final_avg_loss:.4f}")

    return final_avg_loss

def main():
    logger = setup_logging()
    logger.info("=== Simple FastLLaMA Training Debug ===")

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Device: {device}")

    # Load tokenizer
    data_config = create_simple_data_config()
    tokenizer = AutoTokenizer.from_pretrained(data_config.tokenizer_name)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    logger.info(f"Tokenizer loaded. Vocab size: {tokenizer.vocab_size}")

    # Create simple model
    config = create_simple_model_config(tokenizer.vocab_size)
    model = FastLLaMAModel(config)
    model.to(device)

    num_params = sum(p.numel() for p in model.parameters())
    logger.info(f"Model created: {num_params/1e6:.1f}M parameters")

    # Test forward pass
    initial_loss = test_model_forward(model, tokenizer, device, logger)

    # Create dataloader
    dataloader_wrapper = FastLLaMADataLoader(data_config, tokenizer)
    train_dataloader = dataloader_wrapper.create_train_dataloader()

    # Create simple optimizer
    optimizer = optim.AdamW(
        model.parameters(),
        lr=1e-3,  # High learning rate for debugging
        weight_decay=0.01
    )

    logger.info(f"Optimizer created with lr=1e-3")

    # Run simple training
    final_loss = simple_training_loop(
        model, train_dataloader, optimizer, device, logger, max_steps=100
    )

    # Test again after training
    logger.info("Testing model after training...")
    final_test_loss = test_model_forward(model, tokenizer, device, logger)

    # Summary
    logger.info("=== Training Summary ===")
    logger.info(f"Initial loss: {initial_loss:.4f}")
    logger.info(f"Final training loss: {final_loss:.4f}")
    logger.info(f"Final test loss: {final_test_loss:.4f}")

    improvement = initial_loss - final_test_loss
    logger.info(f"Loss improvement: {improvement:.4f}")

    if improvement > 0.1:
        logger.info("✅ Model is learning successfully!")
    else:
        logger.warning("❌ Model may not be learning properly!")

    return model, final_loss

if __name__ == "__main__":
    print("🔧 Starting Simple FastLLaMA Training Debug")
    print("📊 Configuration:")
    print("  - Model: Very small (256 hidden, 4 layers)")
    print("  - Features: ALL DISABLED")
    print("  - Training: Basic PyTorch loop")
    print("  - Steps: 100 for quick test")
    print("=" * 50)

    model, loss = main()

    print(f"\n🎯 Debug Results:")
    print(f"  - Final loss: {loss:.4f}")
    print(f"  - Model size: {sum(p.numel() for p in model.parameters())/1e6:.1f}M params")
    print("✅ Debug completed!")
