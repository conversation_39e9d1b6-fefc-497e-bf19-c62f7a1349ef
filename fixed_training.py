"""
Fixed FastLLaMA Training Script

This script fixes the training issues by:
1. Using standard PyTorch optimizers instead of custom ones
2. Simplifying the training loop
3. Disabling problematic advanced features
4. Using proper learning rates and configurations
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
import logging
import time
from pathlib import Path

# Add FastLLaMA to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastllama import FastLLaMAConfig, FastLLaMAModel
from fastllama.data import DataConfig, FastLLaMADataLoader
from transformers import AutoTokenizer

def setup_logging(output_dir):
    """Setup logging."""
    log_dir = Path(output_dir) / "logs"
    log_dir.mkdir(parents=True, exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_dir / "training.log", encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def create_fixed_model_config(vocab_size):
    """Create a working model configuration."""
    return FastLLaMAConfig(
        # Reasonable model size
        hidden_size=768,
        intermediate_size=768*4,
        num_attention_heads=12,
        num_key_value_heads=12,  # No GQA for simplicity
        num_hidden_layers=8,
        vocab_size=vocab_size + 8,
        max_position_embeddings=2048,

        # DISABLE all advanced features that may cause issues
        enable_context_compression=False,
        enable_early_exit=False,
        use_flash_attention=False,
        use_kernel_fusion=False,
        use_gradient_checkpointing=False,
        kv_cache_quantization=False,
        parameter_sharing=False,

        # Standard attention for all layers
        local_layers=[],
        sparse_layers=[],
        hierarchical_layers=[],
        full_attention_layers=list(range(8)),
        early_exit_layers=[],

        # Disable dynamic features
        dynamic_batching=False,
        memory_aware_batching=False,
    )

def create_fixed_data_config():
    """Create working data configuration."""
    return DataConfig(
        dataset_name="HuggingFaceTB/smollm-corpus",
        dataset_config="fineweb-edu-dedup",
        tokenizer_name="D:\\vlinhd11\\Projects\\LCMS_LLM\\MOE-LLM\\v2\\my_tokenizer\\llama_en_vi_tokenizer",
        max_length=512,
        batch_size=4,
        streaming=True,
        num_workers=0,  # Disable multiprocessing for stability
        min_length=400,
        padding="max_length",
        truncation=True,
        filter_empty=True,
        pin_memory=False,
        prefetch_factor=None,  # Must be None when num_workers=0
        drop_last=True,
    )

def train_model(model, dataloader, optimizer, scheduler, device, logger, max_steps=1000):
    """Simple and effective training loop."""
    model.train()

    total_loss = 0
    step = 0
    start_time = time.time()
    best_loss = float('inf')

    logger.info(f"Starting training for {max_steps} steps...")

    for batch in dataloader:
        if step >= max_steps:
            break

        # Move batch to device
        input_ids = batch['input_ids'].to(device)
        labels = batch['labels'].to(device)
        attention_mask = batch.get('attention_mask')
        if attention_mask is not None:
            attention_mask = attention_mask.to(device)

        # Forward pass
        optimizer.zero_grad()

        outputs = model(
            input_ids=input_ids,
            labels=labels,
            attention_mask=attention_mask
        )

        loss = outputs['loss']

        # Check for NaN loss
        if torch.isnan(loss) or torch.isinf(loss):
            logger.warning(f"NaN/Inf loss detected at step {step}, skipping...")
            continue

        # Backward pass
        loss.backward()

        # Gradient clipping
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)

        # Optimizer step
        optimizer.step()
        scheduler.step()

        total_loss += loss.item()
        step += 1

        # Logging
        if step % 10 == 0:
            avg_loss = total_loss / step
            elapsed = time.time() - start_time
            speed = step / elapsed
            current_lr = optimizer.param_groups[0]['lr']

            logger.info(f"Step {step}: loss={loss.item():.4f}, avg_loss={avg_loss:.4f}, "
                       f"lr={current_lr:.6f}, speed={speed:.2f} it/s")

            # Track best loss
            if loss.item() < best_loss:
                best_loss = loss.item()
                logger.info(f"New best loss: {best_loss:.4f}")

        # Memory cleanup
        if step % 100 == 0:
            torch.cuda.empty_cache()

    final_avg_loss = total_loss / step if step > 0 else float('inf')
    logger.info(f"Training completed. Final average loss: {final_avg_loss:.4f}")
    logger.info(f"Best loss achieved: {best_loss:.4f}")

    return final_avg_loss, best_loss

def main():
    # Setup
    output_dir = "./fixed_fastllama_output"
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    logger = setup_logging(output_dir)
    logger.info("=== Fixed FastLLaMA Training ===")
    logger.info(f"Device: {device}")

    # GPU info
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
        logger.info(f"GPU: {gpu_name}")
        logger.info(f"GPU Memory: {gpu_memory:.1f}GB")

    # Load tokenizer
    data_config = create_fixed_data_config()
    tokenizer = AutoTokenizer.from_pretrained(data_config.tokenizer_name)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    logger.info(f"Tokenizer loaded. Vocab size: {tokenizer.vocab_size}")

    # Create model
    config = create_fixed_model_config(tokenizer.vocab_size)
    model = FastLLaMAModel(config)
    model.to(device)

    num_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"Model created: {num_params/1e6:.1f}M total parameters")
    logger.info(f"Trainable parameters: {trainable_params/1e6:.1f}M")

    # Create dataloader
    dataloader_wrapper = FastLLaMADataLoader(data_config, tokenizer)
    train_dataloader = dataloader_wrapper.create_train_dataloader()

    # Create standard PyTorch optimizer
    optimizer = optim.AdamW(
        model.parameters(),
        lr=5e-4,  # Higher learning rate for faster convergence
        weight_decay=0.01,
        betas=(0.9, 0.95),
        eps=1e-8
    )

    # Create learning rate scheduler
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=100,  # Restart every 100 steps
        T_mult=1,
        eta_min=1e-6
    )

    logger.info(f"Optimizer: AdamW with lr=5e-4")
    logger.info(f"Scheduler: CosineAnnealingWarmRestarts")

    # Train model
    try:
        avg_loss, best_loss = train_model(
            model, train_dataloader, optimizer, scheduler, device, logger, max_steps=1000
        )

        # Save model
        model_path = Path(output_dir) / "trained_model"
        model_path.mkdir(parents=True, exist_ok=True)

        # Save model state dict
        torch.save(model.state_dict(), model_path / "pytorch_model.bin")
        torch.save(config, model_path / "config.json")
        tokenizer.save_pretrained(model_path)

        logger.info(f"Model saved to {model_path}")

        # Final summary
        logger.info("=== Training Summary ===")
        logger.info(f"Average loss: {avg_loss:.4f}")
        logger.info(f"Best loss: {best_loss:.4f}")

        if best_loss < 8.0:
            logger.info("✅ Training successful - loss decreased significantly!")
        else:
            logger.warning("⚠️ Training may need more steps or tuning")

        return model, avg_loss, best_loss

    except Exception as e:
        logger.error(f"Training failed: {e}")
        import traceback
        traceback.print_exc()
        raise

    finally:
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

if __name__ == "__main__":
    print("🔧 Starting Fixed FastLLaMA Training")
    print("📊 Configuration:")
    print("  - Model: 768 hidden, 8 layers, 12 heads")
    print("  - Advanced features: DISABLED")
    print("  - Optimizer: Standard AdamW")
    print("  - Learning rate: 5e-4")
    print("  - Steps: 1000")
    print("=" * 50)

    model, avg_loss, best_loss = main()

    print(f"\n🎯 Training Results:")
    print(f"  - Average loss: {avg_loss:.4f}")
    print(f"  - Best loss: {best_loss:.4f}")
    print(f"  - Model size: {sum(p.numel() for p in model.parameters())/1e6:.1f}M params")

    if best_loss < 8.0:
        print("✅ Training successful!")
    else:
        print("⚠️ Training needs improvement")
