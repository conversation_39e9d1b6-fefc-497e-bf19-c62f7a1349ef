"""
Context Compression Module for FastLLaMA.

Implements learned context compression for efficient long-context processing.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple, List
from ..config import FastLLaMAConfig


class AttentionPooling(nn.Module):
    """Attention-based pooling for sequence compression."""

    def __init__(self, hidden_size: int):
        super().__init__()
        self.hidden_size = hidden_size
        self.attention = nn.Linear(hidden_size, 1, bias=False)

    def forward(self, hidden_states: torch.Tensor) -> torch.Tensor:
        """
        Args:
            hidden_states: [batch_size, seq_len, hidden_size]
        Returns:
            pooled: [batch_size, 1, hidden_size]
        """
        # Compute attention weights
        attn_weights = self.attention(hidden_states)  # [batch_size, seq_len, 1]
        attn_weights = F.softmax(attn_weights, dim=1)

        # Apply attention weights
        pooled = torch.sum(attn_weights * hidden_states, dim=1, keepdim=True)
        return pooled


class TransformerEncoder(nn.Module):
    """Lightweight transformer encoder for compression."""

    def __init__(self, config: FastLLaMAConfig, num_layers: int = 4):
        super().__init__()
        self.config = config
        self.num_layers = num_layers

        self.layers = nn.ModuleList([
            TransformerEncoderLayer(config) for _ in range(num_layers)
        ])

    def forward(self, hidden_states: torch.Tensor) -> torch.Tensor:
        for layer in self.layers:
            hidden_states = layer(hidden_states)
        return hidden_states


class TransformerEncoderLayer(nn.Module):
    """Single transformer encoder layer."""

    def __init__(self, config: FastLLaMAConfig):
        super().__init__()
        self.hidden_size = config.hidden_size

        # Self-attention
        self.self_attn = nn.MultiheadAttention(
            embed_dim=config.hidden_size,
            num_heads=config.num_attention_heads // 4,  # Fewer heads for compression
            dropout=0.1,
            batch_first=True
        )

        # Feed-forward network
        self.mlp = nn.Sequential(
            nn.Linear(config.hidden_size, config.hidden_size * 2),
            nn.ReLU(),
            nn.Linear(config.hidden_size * 2, config.hidden_size),
            nn.Dropout(0.1)
        )

        # Layer normalization
        self.norm1 = nn.LayerNorm(config.hidden_size, eps=config.rms_norm_eps)
        self.norm2 = nn.LayerNorm(config.hidden_size, eps=config.rms_norm_eps)

    def forward(self, hidden_states: torch.Tensor) -> torch.Tensor:
        # Self-attention with residual connection
        residual = hidden_states
        hidden_states = self.norm1(hidden_states)
        attn_output, _ = self.self_attn(hidden_states, hidden_states, hidden_states)
        hidden_states = residual + attn_output

        # Feed-forward with residual connection
        residual = hidden_states
        hidden_states = self.norm2(hidden_states)
        hidden_states = residual + self.mlp(hidden_states)

        return hidden_states


class ContextCompressor(nn.Module):
    """
    Context compression module for handling long sequences efficiently.

    Implements the compression strategy from the system design:
    - Learned compression encoder
    - Attention-based summary generation
    - Progressive compression ratios
    """

    def __init__(self, config: FastLLaMAConfig):
        super().__init__()
        self.config = config
        self.compression_ratio = config.compression_ratio
        self.hidden_size = config.hidden_size

        # Compression encoder
        self.encoder = TransformerEncoder(config, config.compression_encoder_layers)

        # Attention pooling for summary generation
        self.pooling = AttentionPooling(config.hidden_size)

        # Progressive compression layers
        if config.progressive_compression:
            self.progressive_layers = nn.ModuleList([
                nn.Linear(config.hidden_size, config.hidden_size) for _ in range(3)
            ])

        # Decompression layers
        self.decompressor = nn.Sequential(
            nn.Linear(config.hidden_size, config.hidden_size * 2),
            nn.ReLU(),
            nn.Linear(config.hidden_size * 2, config.hidden_size),
            nn.Dropout(0.1)
        )

        # Compression quality predictor
        self.quality_predictor = nn.Linear(config.hidden_size, 1)

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Compress long sequence into shorter representation.

        Args:
            hidden_states: [batch_size, seq_len, hidden_size]
            attention_mask: [batch_size, seq_len]

        Returns:
            compressed_states: [batch_size, compressed_len, hidden_size]
            compression_quality: [batch_size, compressed_len]
        """
        batch_size, seq_len, hidden_size = hidden_states.size()

        if seq_len <= self.compression_ratio:
            # No compression needed for short sequences
            quality = torch.ones(batch_size, seq_len, device=hidden_states.device)
            return hidden_states, quality

        # Segment sequence into chunks for compression
        chunks = self._segment_sequence(hidden_states, attention_mask)

        compressed_chunks = []
        quality_scores = []

        for chunk in chunks:
            # Encode chunk
            encoded_chunk = self.encoder(chunk)

            # Generate summary using attention pooling
            summary = self.pooling(encoded_chunk)  # [batch_size, 1, hidden_size]

            # Apply progressive compression if enabled
            if self.config.progressive_compression:
                summary = self._apply_progressive_compression(summary)

            # Predict compression quality
            quality = torch.sigmoid(self.quality_predictor(summary))  # [batch_size, 1, 1]

            compressed_chunks.append(summary)
            quality_scores.append(quality.squeeze(-1))  # [batch_size, 1]

        # Concatenate compressed chunks
        compressed_states = torch.cat(compressed_chunks, dim=1)  # [batch_size, num_chunks, hidden_size]
        compression_quality = torch.cat(quality_scores, dim=1)  # [batch_size, num_chunks]

        # PHASE 2 FIX: Always apply decompressor during training for gradient flow
        if self.training:
            # Apply decompression transformation to ensure gradient flow
            decompressed_states = self.decompressor(compressed_states)
            # Use a weighted combination of compressed and decompressed states
            # This ensures both paths receive gradients during training
            alpha = 0.8  # Weight for compressed states
            combined_states = alpha * compressed_states + (1 - alpha) * decompressed_states
            return combined_states, compression_quality

        return compressed_states, compression_quality

    def decompress(
        self,
        compressed_states: torch.Tensor,
        target_length: int
    ) -> torch.Tensor:
        """
        Decompress compressed representation back to longer sequence.

        Args:
            compressed_states: [batch_size, compressed_len, hidden_size]
            target_length: Target sequence length for decompression

        Returns:
            decompressed_states: [batch_size, target_length, hidden_size]
        """
        batch_size, compressed_len, hidden_size = compressed_states.size()

        # Apply decompression transformation
        decompressed = self.decompressor(compressed_states)

        # Expand to target length using interpolation
        if target_length > compressed_len:
            # Upsample using linear interpolation
            decompressed = decompressed.transpose(1, 2)  # [batch_size, hidden_size, compressed_len]
            decompressed = F.interpolate(
                decompressed,
                size=target_length,
                mode='linear',
                align_corners=False
            )
            decompressed = decompressed.transpose(1, 2)  # [batch_size, target_length, hidden_size]

        return decompressed

    def _segment_sequence(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None
    ) -> List[torch.Tensor]:
        """Segment sequence into chunks for compression."""
        batch_size, seq_len, hidden_size = hidden_states.size()
        chunk_size = self.compression_ratio

        chunks = []
        for i in range(0, seq_len, chunk_size):
            end_idx = min(i + chunk_size, seq_len)
            chunk = hidden_states[:, i:end_idx, :]

            # Pad chunk if necessary
            if chunk.size(1) < chunk_size:
                padding_size = chunk_size - chunk.size(1)
                padding = torch.zeros(batch_size, padding_size, hidden_size, device=chunk.device)
                chunk = torch.cat([chunk, padding], dim=1)

            chunks.append(chunk)

        return chunks

    def _apply_progressive_compression(self, summary: torch.Tensor) -> torch.Tensor:
        """Apply progressive compression with multiple stages."""
        for layer in self.progressive_layers:
            summary = layer(summary)
            summary = F.relu(summary)
        return summary

    def get_compression_stats(self, original_length: int, compressed_length: int) -> dict:
        """Get compression statistics."""
        compression_ratio = original_length / compressed_length
        memory_savings = 1 - (compressed_length / original_length)

        return {
            "original_length": original_length,
            "compressed_length": compressed_length,
            "compression_ratio": compression_ratio,
            "memory_savings": memory_savings
        }
