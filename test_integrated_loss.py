"""
Test the integrated loss function to see if early exit gradients flow properly.
"""

import os
import sys
import torch
import torch.nn as nn

# Add FastLLaMA to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastllama import FastLLaMAConfig, FastLLaMAModel

def test_integrated_loss():
    """Test the model's integrated loss function."""
    print("🧪 Testing Integrated Loss Function")
    print("=" * 50)
    
    # Create test config
    config = FastLLaMAConfig(
        hidden_size=256,
        intermediate_size=256*4,
        num_attention_heads=4,
        num_key_value_heads=2,
        num_hidden_layers=4,
        vocab_size=1000,
        max_position_embeddings=1024,

        # Enable ALL advanced features
        enable_context_compression=True,
        enable_early_exit=True,
        kv_cache_quantization=True,
        parameter_sharing=True,

        # Configure attention layers
        local_layers=[0, 1],
        sparse_layers=[2],
        hierarchical_layers=[3],
        full_attention_layers=[],

        # Early exit configuration
        early_exit_layers=[1, 3],
        confidence_threshold=0.7,
    )
    
    model = FastLLaMAModel(config)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)
    
    # Create test input
    batch_size = 2
    seq_length = 512
    input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_length)).to(device)
    labels = input_ids.clone()
    
    model.train()
    model.zero_grad()
    
    print("🔍 Testing model's integrated loss computation...")
    
    # Forward pass with early exit enabled
    outputs = model(input_ids=input_ids, labels=labels, enable_early_exit=True)
    
    print(f"✅ Model outputs keys: {list(outputs.keys())}")
    print(f"✅ Loss: {outputs['loss'].item():.4f}")
    print(f"✅ Early exit outputs: {len(outputs.get('early_exit_outputs', []))}")
    print(f"✅ Compression quality: {outputs.get('compression_quality') is not None}")
    
    # Backward pass using model's integrated loss
    loss = outputs['loss']
    loss.backward()
    
    # Check gradients
    print("\n🔍 Checking gradients after integrated loss...")
    
    total_params = 0
    grad_params = 0
    no_grad_params = []
    
    # Check early exit parameters specifically
    early_exit_grad_count = 0
    early_exit_total_count = 0
    
    # Check KV quantizer parameters specifically
    kv_quantizer_grad_count = 0
    kv_quantizer_total_count = 0
    
    for name, param in model.named_parameters():
        total_params += 1
        if param.grad is not None:
            grad_params += 1
        else:
            no_grad_params.append(name)
        
        # Count early exit parameters
        if 'early_exit_head' in name or 'confidence_head' in name:
            early_exit_total_count += 1
            if param.grad is not None:
                early_exit_grad_count += 1
        
        # Count KV quantizer parameters
        if 'k_quantizer' in name or 'v_quantizer' in name:
            kv_quantizer_total_count += 1
            if param.grad is not None:
                kv_quantizer_grad_count += 1
    
    print(f"✅ Total gradient coverage: {grad_params}/{total_params} ({100*grad_params/total_params:.1f}%)")
    print(f"✅ Early exit gradient coverage: {early_exit_grad_count}/{early_exit_total_count}")
    print(f"✅ KV quantizer gradient coverage: {kv_quantizer_grad_count}/{kv_quantizer_total_count}")
    
    if no_grad_params:
        print(f"\n⚠️ Parameters without gradients ({len(no_grad_params)}):")
        for param in no_grad_params[:10]:  # Show first 10
            print(f"    - {param}")
        if len(no_grad_params) > 10:
            print(f"    ... and {len(no_grad_params) - 10} more")
    
    # Test if early exit loss is being computed
    if outputs.get('early_exit_outputs'):
        print(f"\n✅ Early exit outputs found: {len(outputs['early_exit_outputs'])}")
        for i, early_output in enumerate(outputs['early_exit_outputs']):
            confidence = early_output['confidence'].mean().item()
            print(f"    Exit {i}: confidence={confidence:.3f}")
    else:
        print("\n❌ No early exit outputs found")
    
    return grad_params == total_params

def test_manual_early_exit_loss():
    """Test manual early exit loss computation to compare."""
    print("\n🧪 Testing Manual Early Exit Loss")
    print("=" * 50)
    
    config = FastLLaMAConfig(
        hidden_size=256,
        num_attention_heads=4,
        num_key_value_heads=2,
        num_hidden_layers=4,
        vocab_size=1000,
        kv_cache_quantization=True,
        local_layers=[0, 1],
        sparse_layers=[2],
        hierarchical_layers=[3],
        early_exit_layers=[1, 3],
    )
    
    model = FastLLaMAModel(config)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)
    
    batch_size = 2
    seq_length = 512
    input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_length)).to(device)
    labels = input_ids.clone()
    
    model.train()
    model.zero_grad()
    
    # Forward pass without labels to get raw outputs
    outputs = model(input_ids=input_ids, enable_early_exit=True)
    
    # Manually compute comprehensive loss
    main_logits = outputs['logits']
    early_exit_outputs = outputs.get('early_exit_outputs', [])
    
    # Main loss
    shift_logits = main_logits[..., :-1, :].contiguous()
    shift_labels = labels[..., 1:].contiguous()
    loss_fct = nn.CrossEntropyLoss(ignore_index=-100)
    main_loss = loss_fct(shift_logits.view(-1, config.vocab_size), shift_labels.view(-1))
    
    total_loss = main_loss
    print(f"✅ Main loss: {main_loss.item():.4f}")
    
    # Early exit losses
    if early_exit_outputs:
        early_exit_loss = 0
        confidence_loss = 0
        
        for i, early_output in enumerate(early_exit_outputs):
            early_logits = early_output["logits"]
            early_shift_logits = early_logits[..., :-1, :].contiguous()
            early_shift_labels = labels[..., 1:].contiguous()
            
            min_len = min(early_shift_logits.size(1), early_shift_labels.size(1))
            early_shift_logits = early_shift_logits[:, :min_len, :]
            early_shift_labels = early_shift_labels[:, :min_len]
            
            early_loss = loss_fct(
                early_shift_logits.view(-1, config.vocab_size),
                early_shift_labels.view(-1)
            )
            early_exit_loss += early_loss
            
            confidence = early_output["confidence"]
            target_confidence = torch.full_like(confidence, 0.6)
            conf_loss = nn.MSELoss()(confidence, target_confidence)
            confidence_loss += conf_loss
            
            print(f"✅ Early exit {i} loss: {early_loss.item():.4f}")
            print(f"✅ Confidence {i} loss: {conf_loss.item():.4f}")
        
        total_loss += 0.1 * early_exit_loss
        total_loss += 0.05 * confidence_loss
        
        print(f"✅ Total early exit loss: {early_exit_loss.item():.4f}")
        print(f"✅ Total confidence loss: {confidence_loss.item():.4f}")
    
    print(f"✅ Total loss: {total_loss.item():.4f}")
    
    # Backward pass
    total_loss.backward()
    
    # Check early exit gradients
    early_exit_grad_count = 0
    early_exit_total_count = 0
    
    for name, param in model.named_parameters():
        if 'early_exit_head' in name or 'confidence_head' in name:
            early_exit_total_count += 1
            if param.grad is not None:
                early_exit_grad_count += 1
                print(f"✅ {name}: HAS GRADIENT")
            else:
                print(f"❌ {name}: NO GRADIENT")
    
    print(f"\n✅ Manual early exit gradient coverage: {early_exit_grad_count}/{early_exit_total_count}")
    
    return early_exit_grad_count == early_exit_total_count

if __name__ == "__main__":
    print("🔧 Testing Integrated Loss Function")
    print("=" * 70)
    
    success1 = test_integrated_loss()
    success2 = test_manual_early_exit_loss()
    
    print("\n" + "=" * 70)
    print("📊 SUMMARY")
    print("=" * 70)
    
    if success1:
        print("✅ Integrated loss: ALL GRADIENTS FLOWING")
    else:
        print("❌ Integrated loss: GRADIENT ISSUES REMAIN")
    
    if success2:
        print("✅ Manual early exit loss: ALL GRADIENTS FLOWING")
    else:
        print("❌ Manual early exit loss: GRADIENT ISSUES REMAIN")
    
    if success1 and success2:
        print("\n🎉 ALL TESTS PASSED - GRADIENT FLOW WORKING!")
    else:
        print("\n🔧 ADDITIONAL FIXES NEEDED")
