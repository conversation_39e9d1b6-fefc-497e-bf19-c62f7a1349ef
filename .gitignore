# Python__pycache__/
*.py[cod]*$py.class
*.so.Python
build/
develop-eggs/dist/downloads/
eggs/.eggs/
lib/
lib64/parts/sdist/
var/wheels/
*.egg-info/
.installed.cfg*.egg
# Virtual Environmentvenv/
env/ENV/
.env.venv
# IDE
.idea/.vscode/
*.swp
*.swo.DS_Store
# Project specificlogs/
checkpoints/
outputs/
benchmark_results
fastllama_output
**.pyc
fastllama_advanced_output
fastllama_copied_weights
fastllama_updated_trainer_test
fastllama_wikitext_output
fastllama_with_early_exit
test_weight_copy_output
fastllama_fixed_output
/*output*/
