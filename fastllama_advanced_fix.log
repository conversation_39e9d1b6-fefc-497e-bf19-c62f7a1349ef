2025-05-30 21:04:34,475 - INFO - Using device: cuda
2025-05-30 21:04:37,591 - INFO -   - layers.0.self_attn.k_quantizer
2025-05-30 21:04:37,591 - INFO -   - layers.0.self_attn.v_quantizer
2025-05-30 21:04:37,591 - INFO -   - layers.0.self_attn.group_adapter.weight
2025-05-30 21:04:37,591 - INFO -   - layers.0.self_attn.group_adapter.bias
2025-05-30 21:04:37,591 - INFO -   - layers.1.self_attn.k_quantizer
2025-05-30 21:04:37,591 - INFO -   - layers.1.self_attn.v_quantizer
2025-05-30 21:04:37,593 - INFO -   - layers.1.self_attn.group_adapter.weight
2025-05-30 21:04:37,593 - INFO -   - layers.1.self_attn.group_adapter.bias
2025-05-30 21:04:37,593 - INFO -   - layers.2.self_attn.k_quantizer
2025-05-30 21:04:37,593 - INFO -   - layers.2.self_attn.v_quantizer
2025-05-30 21:04:37,593 - INFO -   ... and 22 more
2025-05-30 21:04:38,010 - INFO -   - Foundation phase: 60%
2025-05-30 21:04:38,010 - INFO -   - Long context phase: 30%
2025-05-30 21:04:38,010 - INFO -   - Efficiency phase: 10%
2025-05-30 21:04:38,020 - INFO - Initial memory usage: {'gpu_allocated_gb': 1.2524456977844238, 'gpu_reserved_gb': 1.28125, 'gpu_max_allocated_gb': 1.2524456977844238, 'gpu_max_reserved_gb': 1.28125, 'cpu_rss_gb': 2.004840850830078, 'cpu_vms_gb': 5.056461334228516, 'cpu_percent': 6.300683984655092, 'system_total_gb': 31.819416046142578, 'system_available_gb': 8.376800537109375, 'system_used_percent': 73.7}
2025-05-30 21:04:38,020 - INFO - Starting FastLLaMA training...
2025-05-30 21:04:38,020 - INFO - Starting foundation phase...
2025-05-30 21:04:46,049 - INFO - Loaded dataset: HuggingFaceTB/smollm-corpus
2025-05-30 21:04:46,051 - INFO - Initialized StreamingTextDataset with HuggingFaceTB/smollm-corpus
2025-05-30 21:05:32,425 - INFO - Step 25: loss=2.7142, lr=0.0000030, seq_len=512, memory=3.83GB
2025-05-30 21:05:57,954 - INFO - Step 50: loss=2.5801, lr=0.0000060, seq_len=512, memory=3.83GB
2025-05-30 21:06:21,848 - INFO - Step 75: loss=2.5181, lr=0.0000090, seq_len=512, memory=3.83GB
2025-05-30 21:06:46,129 - INFO - Step 100: loss=2.4987, lr=0.0000125, seq_len=512, memory=3.83GB
2025-05-30 21:07:09,437 - INFO - Step 125: loss=2.4721, lr=0.0000155, seq_len=512, memory=3.83GB
2025-05-30 21:07:33,949 - INFO - Step 150: loss=2.4014, lr=0.0000185, seq_len=512, memory=3.83GB
2025-05-30 21:07:58,339 - INFO - Step 175: loss=2.4064, lr=0.0000215, seq_len=512, memory=3.83GB
2025-05-30 21:08:23,132 - INFO - Step 200: loss=2.2499, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:08:44,937 - INFO - Step 225: loss=2.1701, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:09:05,300 - INFO - Step 250: loss=2.2317, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:09:12,958 - INFO - Checkpoint saved to ./fastllama_advanced_fixed_output\checkpoint-250
2025-05-30 21:09:37,747 - INFO - Step 275: loss=2.0948, lr=0.0000249, seq_len=512, memory=3.83GB
2025-05-30 21:10:01,028 - INFO - Step 300: loss=2.0658, lr=0.0000248, seq_len=512, memory=3.83GB
2025-05-30 21:10:02,217 - INFO - Starting long_context phase...
2025-05-30 21:10:07,193 - INFO - Loaded dataset: HuggingFaceTB/smollm-corpus
2025-05-30 21:10:07,193 - INFO - Initialized StreamingTextDataset with HuggingFaceTB/smollm-corpus
2025-05-30 21:10:51,011 - INFO - Step 325: loss=1.9688, lr=0.0000247, seq_len=1024, memory=3.83GB
2025-05-30 21:11:15,382 - INFO - Step 350: loss=1.8638, lr=0.0000246, seq_len=1024, memory=3.83GB
2025-05-30 21:11:39,220 - INFO - Step 375: loss=1.9180, lr=0.0000244, seq_len=1024, memory=3.83GB
2025-05-30 21:12:04,118 - INFO - Step 400: loss=1.9332, lr=0.0000242, seq_len=1024, memory=3.83GB
2025-05-30 21:12:27,934 - INFO - Step 425: loss=1.9132, lr=0.0000241, seq_len=1024, memory=3.83GB
2025-05-30 21:12:51,824 - INFO - Step 450: loss=1.8583, lr=0.0000238, seq_len=1024, memory=3.83GB
2025-05-30 21:12:52,909 - INFO - Starting efficiency phase...
2025-05-30 21:12:58,075 - INFO - Loaded dataset: HuggingFaceTB/smollm-corpus
2025-05-30 21:12:58,075 - INFO - Initialized StreamingTextDataset with HuggingFaceTB/smollm-corpus
2025-05-30 21:13:44,699 - INFO - Step 475: loss=1.8250, lr=0.0000236, seq_len=1024, memory=3.83GB
2025-05-30 21:14:09,622 - INFO - Step 500: loss=1.7461, lr=0.0000234, seq_len=1024, memory=3.83GB
2025-05-30 21:14:17,241 - INFO - Checkpoint saved to ./fastllama_advanced_fixed_output\checkpoint-500
2025-05-30 21:14:18,420 - INFO - Training completed!
2025-05-30 21:14:18,422 - INFO - Final training metrics: {'loss': [2.7142324447631836, 2.5800936222076416, 2.5180857181549072, 2.4986960887908936, 2.4720544815063477, 2.4013516902923584, 2.4063615798950195, 2.2499423027038574, 2.1700825691223145, 2.2317471504211426, 2.0947957038879395, 2.065802574157715, 1.9688242673873901, 1.863800287246704, 1.918022632598877, 1.933152198791504, 1.9132051467895508, 1.8582638502120972, 1.8249660730361938, 1.7461360692977905], 'learning_rate': [3e-06, 6e-06, 9e-06, 1.25e-05, 1.55e-05, 1.85e-05, 2.15e-05, 2.5e-05, 2.498903537623573e-05, 2.4956160740618806e-05, 2.4901433766430975e-05, 2.4810096912652604e-05, 2.47084034791771e-05, 2.4585292357555746e-05, 2.4440979526529295e-05, 2.4246157759823855e-05, 2.4056825420153917e-05, 2.384721848166136e-05, 2.361770466614122e-05, 2.3368686618815238e-05], 'sequence_length': [512, 512, 512, 512, 512, 512, 512, 512, 512, 512, 512, 512, 1024, 1024, 1024, 1024, 1024, 1024, 1024, 1024], 'memory_usage': [3.828554630279541, 3.827732563018799, 3.827732563018799, 3.827732563018799, 3.827732563018799, 3.827732563018799, 3.827732563018799, 3.827732563018799, 3.827732563018799, 3.827732563018799, 3.827732563018799, 3.827732563018799, 3.827732563018799, 3.827732563018799, 3.827732563018799, 3.827732563018799, 3.827732563018799, 3.827732563018799, 3.827732563018799, 3.827732563018799], 'throughput': []}
2025-05-30 21:14:18,434 - INFO - Final memory usage: {'gpu_allocated_gb': 3.3176045417785645, 'gpu_reserved_gb': 4.109375, 'gpu_max_allocated_gb': 3.31765079498291, 'gpu_max_reserved_gb': 4.109375, 'cpu_rss_gb': 1.8144302368164062, 'cpu_vms_gb': 7.739891052246094, 'cpu_percent': 5.702273838668912, 'system_total_gb': 31.819416046142578, 'system_available_gb': 9.060752868652344, 'system_used_percent': 71.5}
2025-05-30 21:14:22,878 - INFO - 
--- Advanced Test 1 ---
2025-05-30 21:14:22,878 - INFO - Prompt: The future of artificial intelligence is
2025-05-30 21:14:23,044 - INFO - 
--- Advanced Test 2 ---
2025-05-30 21:14:23,044 - INFO - Prompt: In a world where technology advances rapidly,
2025-05-30 21:30:54,452 - INFO - 🚀 FastLLaMA Advanced Features Fix & Training
2025-05-30 21:30:54,452 - INFO - Using device: cuda
2025-05-30 21:30:54,452 - INFO - 📚 Creating advanced data configuration...
2025-05-30 21:30:54,579 - INFO - ✅ Tokenizer loaded. Vocab size: 64811
2025-05-30 21:30:54,579 - INFO - 🧠 Creating advanced FastLLaMA model with ALL features enabled...
2025-05-30 21:30:57,471 - INFO - 🔧 Fixing advanced features...
2025-05-30 21:30:57,471 - INFO - 🔧 Fixing attention weights initialization...
2025-05-30 21:30:57,472 - INFO - ✅ Fixed 32 attention parameters:
2025-05-30 21:30:57,472 - INFO -   - layers.0.self_attn.k_quantizer
2025-05-30 21:30:57,472 - INFO -   - layers.0.self_attn.v_quantizer
2025-05-30 21:30:57,472 - INFO -   - layers.0.self_attn.group_adapter.weight
2025-05-30 21:30:57,472 - INFO -   - layers.0.self_attn.group_adapter.bias
2025-05-30 21:30:57,472 - INFO -   - layers.1.self_attn.k_quantizer
2025-05-30 21:30:57,472 - INFO -   - layers.1.self_attn.v_quantizer
2025-05-30 21:30:57,472 - INFO -   - layers.1.self_attn.group_adapter.weight
2025-05-30 21:30:57,472 - INFO -   - layers.1.self_attn.group_adapter.bias
2025-05-30 21:30:57,472 - INFO -   - layers.2.self_attn.k_quantizer
2025-05-30 21:30:57,472 - INFO -   - layers.2.self_attn.v_quantizer
2025-05-30 21:30:57,472 - INFO -   ... and 22 more
2025-05-30 21:30:57,474 - INFO - 🔧 Fixing context compression gradients...
2025-05-30 21:30:57,475 - INFO - ✅ All compression parameters already have gradients
2025-05-30 21:30:57,475 - INFO - 🔧 Fixing early exit mechanism...
2025-05-30 21:30:57,475 - INFO - ✅ All early exit parameters already have gradients
2025-05-30 21:30:57,893 - INFO - ✅ Advanced model created with 333.3M total parameters
2025-05-30 21:30:57,893 - INFO - ✅ Trainable parameters: 333.3M (100.0%)
2025-05-30 21:30:57,893 - INFO - 🔍 Validating advanced features...
2025-05-30 21:30:57,893 - INFO - ✅ Hierarchical attention layers: 0
2025-05-30 21:30:57,894 - INFO - ✅ Attention weights parameters: 0
2025-05-30 21:30:57,894 - INFO - ✅ Context compression enabled: True
2025-05-30 21:30:57,894 - INFO - ✅ Context compressor found: True
2025-05-30 21:30:57,894 - INFO - ✅ Early exit enabled: True
2025-05-30 21:30:57,894 - INFO - ✅ Early exit heads: 3
2025-05-30 21:30:57,894 - INFO - ✅ GQA enabled: True
2025-05-30 21:30:57,896 - INFO - ✅ KV quantizers found: 8
2025-05-30 21:30:57,896 - INFO - ⚙️ Setting up advanced training configuration...
2025-05-30 21:30:57,896 - INFO - 🏋️ Creating trainer...
2025-05-30 21:30:57,898 - INFO - 🚀 Starting advanced features training...
2025-05-30 21:30:57,899 - INFO - 📊 Training phases:
2025-05-30 21:30:57,899 - INFO -   - Foundation phase: 60%
2025-05-30 21:30:57,899 - INFO -   - Long context phase: 30%
2025-05-30 21:30:57,899 - INFO -   - Efficiency phase: 10%
2025-05-30 21:30:57,908 - INFO - Initial memory usage: {'gpu_allocated_gb': 1.2524456977844238, 'gpu_reserved_gb': 1.28125, 'gpu_max_allocated_gb': 1.2524456977844238, 'gpu_max_reserved_gb': 1.28125, 'cpu_rss_gb': 2.0059967041015625, 'cpu_vms_gb': 5.0524139404296875, 'cpu_percent': 6.304316525459136, 'system_total_gb': 31.819416046142578, 'system_available_gb': 6.472381591796875, 'system_used_percent': 79.7}
2025-05-30 21:30:57,908 - INFO - Starting FastLLaMA training...
2025-05-30 21:30:57,916 - INFO - Starting foundation phase...
2025-05-30 21:31:04,848 - INFO - Loaded dataset: HuggingFaceTB/smollm-corpus
2025-05-30 21:31:04,848 - INFO - Initialized StreamingTextDataset with HuggingFaceTB/smollm-corpus
2025-05-30 21:31:49,339 - INFO - Step 25: loss=2.7295, lr=0.0000030, seq_len=512, memory=3.83GB
2025-05-30 21:32:15,000 - INFO - Step 50: loss=2.5892, lr=0.0000060, seq_len=512, memory=3.83GB
2025-05-30 21:32:38,467 - INFO - Step 75: loss=2.5549, lr=0.0000090, seq_len=512, memory=3.83GB
2025-05-30 21:33:03,080 - INFO - Step 100: loss=2.5191, lr=0.0000125, seq_len=512, memory=3.83GB
2025-05-30 21:33:26,681 - INFO - Step 125: loss=2.4934, lr=0.0000155, seq_len=512, memory=3.83GB
2025-05-30 21:33:50,650 - INFO - Step 150: loss=2.4069, lr=0.0000185, seq_len=512, memory=3.83GB
2025-05-30 21:34:14,520 - INFO - Step 175: loss=2.4115, lr=0.0000215, seq_len=512, memory=3.83GB
2025-05-30 21:34:38,883 - INFO - Step 200: loss=2.2625, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:35:00,527 - INFO - Step 225: loss=2.2095, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:35:21,535 - INFO - Step 250: loss=2.2528, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:35:29,906 - INFO - Checkpoint saved to ./fastllama_advanced_fixed_output\checkpoint-250
2025-05-30 21:35:54,494 - INFO - Step 275: loss=2.1203, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:36:17,854 - INFO - Step 300: loss=2.0722, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:36:40,252 - INFO - Step 325: loss=2.0061, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:37:02,663 - INFO - Step 350: loss=2.0841, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:37:24,893 - INFO - Step 375: loss=2.0238, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:37:47,118 - INFO - Step 400: loss=1.9549, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:38:12,446 - INFO - Step 425: loss=2.1505, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:38:35,791 - INFO - Step 450: loss=2.2064, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:38:59,274 - INFO - Step 475: loss=1.9733, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:39:24,339 - INFO - Step 500: loss=2.0417, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:39:32,835 - INFO - Checkpoint saved to ./fastllama_advanced_fixed_output\checkpoint-500
2025-05-30 21:39:57,095 - INFO - Step 525: loss=2.0430, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:40:20,146 - INFO - Step 550: loss=1.9019, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:40:44,501 - INFO - Step 575: loss=1.9375, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:41:10,171 - INFO - Step 600: loss=1.9986, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:41:34,102 - INFO - Step 625: loss=1.9945, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:41:59,024 - INFO - Step 650: loss=2.0075, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:42:22,491 - INFO - Step 675: loss=2.0202, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:42:46,618 - INFO - Step 700: loss=1.9558, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:43:12,192 - INFO - Step 725: loss=2.0282, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:43:36,844 - INFO - Step 750: loss=1.9517, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:43:44,864 - INFO - Checkpoint saved to ./fastllama_advanced_fixed_output\checkpoint-750
2025-05-30 21:44:08,695 - INFO - Step 775: loss=2.0477, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:44:32,388 - INFO - Step 800: loss=2.0445, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:44:56,320 - INFO - Step 825: loss=2.0009, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:45:19,603 - INFO - Step 850: loss=1.8821, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:45:43,311 - INFO - Step 875: loss=1.8470, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:46:06,382 - INFO - Step 900: loss=1.8825, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:46:31,298 - INFO - Step 925: loss=1.9380, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:46:52,622 - INFO - Step 950: loss=1.8640, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:47:17,754 - INFO - Step 975: loss=1.9689, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:47:41,487 - INFO - Step 1000: loss=1.9782, lr=0.0000250, seq_len=512, memory=3.83GB
2025-05-30 21:47:50,141 - INFO - Checkpoint saved to ./fastllama_advanced_fixed_output\checkpoint-1000
