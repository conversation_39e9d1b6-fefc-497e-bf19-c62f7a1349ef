"""
Quick Speed Test for FastLLaMA - RTX 4070 12GB Optimized

This script runs a quick training test to verify speed optimizations work
before running the full real-world training script.

Target: Verify 3-5x speed improvement (1.1 -> 3-5 it/s)
"""

import os
import sys
import torch
import time
import logging
from pathlib import Path

# Add FastLLaMA to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastllama import FastLLaMAConfig, FastLLaMAModel
from fastllama.training import <PERSON><PERSON>aMATrainer, TrainingArguments
from fastllama.utils import get_memory_stats
from fastllama.data import DataConfig
from transformers import AutoTokenizer

def setup_logging():
    """Setup simple logging for speed test."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )
    return logging.getLogger(__name__)

def apply_speed_optimizations():
    """Apply all speed optimizations."""
    torch.backends.cudnn.benchmark = True
    torch.backends.cuda.matmul.allow_tf32 = True
    torch.backends.cudnn.allow_tf32 = True
    torch.cuda.empty_cache()
    
    # Disable CUDA graph optimization to prevent tensor overwrite issues
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:128"
    os.environ["CUDA_LAUNCH_BLOCKING"] = "0" 
    os.environ["TOKENIZERS_PARALLELISM"] = "true"
    #os.environ["PYTORCH_CUDA_GRAPH_DEBUG"] = "1"  # Add debug info

def create_speed_test_config():
    """Create configuration optimized for speed testing."""
    
    # Data config
    data_config = DataConfig(
        dataset_name="HuggingFaceTB/smollm-corpus",
        dataset_config="fineweb-edu-dedup",
        tokenizer_name="D:\\vlinhd11\\Projects\\LCMS_LLM\\MOE-LLM\\v2\\my_tokenizer\\llama_en_vi_tokenizer",
        max_length=512,
        batch_size=4,        # 4x larger than baseline
        streaming=True,
        num_workers=1,       # 2x more workers
        min_length=400,
        padding="longest",
        truncation=True,
        filter_empty=True,
        pin_memory=True,     # Speed optimization
        prefetch_factor=4,   # Speed optimization
        drop_last=True,
    )
    
    # Model config with advanced features
    def create_model_config(vocab_size):
        return FastLLaMAConfig(
            hidden_size=768,
            intermediate_size=768*4,
            num_attention_heads=8,
            num_key_value_heads=4,
            num_hidden_layers=8,
            vocab_size=vocab_size + 8,
            max_position_embeddings=4096,

            # ALL advanced features enabled
            enable_context_compression=True,
            enable_early_exit=True,
            
            # Speed optimizations
            use_flash_attention=True,
            use_kernel_fusion=True,
            use_mixed_precision=True,
            use_gradient_checkpointing=True,
            gradient_checkpointing_ratio=0.3,

            # Memory optimizations
            kv_cache_quantization=True,
            parameter_sharing=True,
            
            # Attention settings
            local_attention_window=512,
            sparse_attention_stride=4,
            compression_ratio=8,
            early_exit_layers=[2, 4, 6],
            confidence_threshold=0.8,
            compression_encoder_layers=2,
            progressive_compression=True,
            local_layers=[1, 2],
            sparse_layers=[3, 4],
            hierarchical_layers=[5, 6],
            full_attention_layers=[7, 8],
            dynamic_batching=True,
            memory_aware_batching=True,
            max_batch_size=16,
        )
    
    # Training args for speed test
    def create_training_args(output_dir):
        return TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=1,
            max_steps=50000,       # Quick test - 100 steps
            per_device_train_batch_size=4,
            per_device_eval_batch_size=8,
            gradient_accumulation_steps=1,  # No accumulation for speed

            learning_rate=1e-4,
            weight_decay=0.01,
            warmup_steps=10,

            use_mixed_precision=True,
            gradient_checkpointing=True,
            max_grad_norm=1.0,

            # Fixed sequence length for speed test
            initial_seq_length=512,
            max_seq_length=512,
            seq_length_warmup_steps=0,

            eval_steps=50,
            save_steps=3000,
            logging_steps=10,

            # Only foundation phase for speed test
            foundation_phase_ratio=0.8,
            long_context_phase_ratio=0.1,
            efficiency_phase_ratio=0.1,

            # Minimal advanced features training for speed
            early_exit_loss_weight=0.05,
            confidence_loss_weight=0.02,

            dataset_name="HuggingFaceTB/smollm-corpus",
            dataset_config="fineweb-edu-dedup",
            tokenizer_name="D:\\vlinhd11\\Projects\\LCMS_LLM\\MOE-LLM\\v2\\my_tokenizer\\llama_en_vi_tokenizer",
            streaming=True,
            text_column="text",
            filter_empty=True,
            min_length=400,
        )
    
    return data_config, create_model_config, create_training_args

def run_speed_test():
    """Run quick speed test to verify optimizations."""
    logger = setup_logging()
    logger.info("=== FastLLaMA Quick Speed Test ===")
    
    # Setup
    output_dir = "./fastllama_speed_test_output"
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    logger.info(f"Device: {device}")
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
        logger.info(f"GPU: {gpu_name}")
        logger.info(f"GPU Memory: {gpu_memory:.1f}GB")

    # Apply optimizations
    apply_speed_optimizations()
    logger.info("Speed optimizations applied")

    # Create configurations
    data_config, create_model_config, create_training_args = create_speed_test_config()

    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(data_config.tokenizer_name)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    logger.info(f"Tokenizer loaded. Vocab size: {tokenizer.vocab_size}")

    # Create model
    logger.info("Creating FastLLaMA model with advanced features...")
    config = create_model_config(tokenizer.vocab_size)
    model = FastLLaMAModel(config)
    
    # Compile model for speed
    # if hasattr(torch, 'compile') and torch.__version__ >= "2.0":
    #     logger.info("Compiling model...")
    #     try:
    #         # Use different compilation mode to avoid CUDA graph issues
    #         # model = torch.compile(model, mode="reduce-overhead")
    #         model = torch.compile(
    #             model, 
    #             mode="max-autotune",
    #             fullgraph=False,  # Disable full graph optimization
    #             backend="inductor"
    #         )
    #         logger.info("Model compiled successfully")
    #     except Exception as e:
    #         logger.warning(f"Model compilation failed: {e}")
    
    model.to(device)
    model.train()

    num_params = sum(p.numel() for p in model.parameters())
    logger.info(f"Model: {num_params/1e6:.1f}M parameters")

    # Create training setup
    training_args = create_training_args(output_dir)
    trainer = FastLLaMATrainer(
        model=model,
        config=config,
        args=training_args,
        tokenizer=tokenizer,
        data_config=data_config,
    )

    # Log initial memory
    initial_memory = get_memory_stats()
    logger.info(f"Initial GPU memory: {initial_memory.get('gpu_allocated_gb', 0):.1f}GB")

    # Run speed test
    logger.info("Starting speed test (100 steps)...")
    start_time = time.time()
    
    try:
        # Run training
        training_metrics = trainer.train()
        
        end_time = time.time()
        total_time = end_time - start_time
        total_steps = trainer.global_step
        
        # Calculate speed
        speed = total_steps / total_time
        baseline_speed = 1.1  # Original speed
        improvement = speed / baseline_speed
        
        # Results
        logger.info("=== Speed Test Results ===")
        logger.info(f"Total steps: {total_steps}")
        logger.info(f"Total time: {total_time:.2f}s")
        logger.info(f"Training speed: {speed:.2f} it/s")
        logger.info(f"Baseline speed: {baseline_speed:.2f} it/s")
        logger.info(f"Speed improvement: {improvement:.1f}x")
        
        # Memory usage
        final_memory = get_memory_stats()
        logger.info(f"Final GPU memory: {final_memory.get('gpu_allocated_gb', 0):.1f}GB")
        
        # Evaluation
        if speed >= 3.0:
            logger.info("🎉 SUCCESS: Achieved target speed of 3+ it/s!")
            status = "SUCCESS"
        elif speed >= 2.0:
            logger.info("✅ GOOD: Significant improvement achieved!")
            status = "GOOD"
        else:
            logger.info("⚠️ PARTIAL: Some improvement, may need further optimization")
            status = "PARTIAL"
        
        return {
            'speed': speed,
            'improvement': improvement,
            'status': status,
            'memory_gb': final_memory.get('gpu_allocated_gb', 0),
            'total_steps': total_steps,
            'total_time': total_time
        }

    except Exception as e:
        logger.error(f"Speed test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

    finally:
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

def main():
    print("🚀 FastLLaMA Quick Speed Test")
    print("📊 Testing speed optimizations for RTX 4070 12GB")
    print("🎯 Target: 3-5x speed improvement (1.1 -> 3-5 it/s)")
    print("⏱️ Duration: ~2-5 minutes (100 steps)")
    print("=" * 50)
    
    results = run_speed_test()
    
    if results:
        print(f"\n🎉 Speed Test Completed!")
        print(f"📈 Results:")
        print(f"  - Speed: {results['speed']:.2f} it/s")
        print(f"  - Improvement: {results['improvement']:.1f}x faster")
        print(f"  - Status: {results['status']}")
        print(f"  - Memory: {results['memory_gb']:.1f}GB")
        print(f"  - Steps: {results['total_steps']}")
        print(f"  - Time: {results['total_time']:.1f}s")
        
        if results['status'] == "SUCCESS":
            print("\n✅ Ready for full real-world training!")
            print("💡 Run: python real_world_fast_training.py")
        else:
            print("\n⚠️ Consider further optimizations before full training")
    else:
        print("\n❌ Speed test failed. Check logs for details.")

if __name__ == "__main__":
    main()
