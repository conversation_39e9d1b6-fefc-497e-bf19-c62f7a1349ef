"""
Test script for FastLLaMA text generation quality.

This script loads a trained FastLLaMA model and tests its text generation
capabilities with various prompts to verify the training fixes worked.
"""

import os
import sys
import torch
import json
from pathlib import Path

# Add FastLLaMA to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastllama import FastLLaMAConfig, FastLLaMAModel
from transformers import AutoTokenizer

def load_model(model_path):
    """Load FastLLaMA model from path."""
    model_path = Path(model_path)
    
    print(f"📂 Loading model from: {model_path}")
    
    # Load config
    config_path = model_path / "config.json"
    if config_path.exists():
        with open(config_path, 'r') as f:
            config_dict = json.load(f)
        config = FastLLaMAConfig(**config_dict)
        print(f"✅ Config loaded")
    else:
        print(f"❌ Config file not found: {config_path}")
        return None, None, None
    
    # Load tokenizer
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        print(f"✅ Tokenizer loaded (vocab size: {tokenizer.vocab_size})")
    except Exception as e:
        print(f"❌ Failed to load tokenizer: {e}")
        return None, None, None
    
    # Load model
    model = FastLLaMAModel(config)
    
    # Load weights
    weights_path = model_path / "pytorch_model.bin"
    if weights_path.exists():
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        state_dict = torch.load(weights_path, map_location=device)
        model.load_state_dict(state_dict)
        model.to(device)
        model.eval()
        print(f"✅ Model loaded on {device}")
    else:
        print(f"❌ Model weights not found: {weights_path}")
        return None, None, None
    
    return model, tokenizer, config

def generate_text(model, tokenizer, prompt, max_new_tokens=50, temperature=0.7):
    """Generate text using the model."""
    device = next(model.parameters()).device
    
    # Tokenize prompt
    inputs = tokenizer(prompt, return_tensors="pt").to(device)
    input_ids = inputs.input_ids
    
    # Simple greedy generation
    generated_ids = input_ids.clone()
    
    with torch.no_grad():
        for _ in range(max_new_tokens):
            # Forward pass
            outputs = model(input_ids=generated_ids)
            logits = outputs['logits']
            
            # Get next token (with temperature)
            next_token_logits = logits[0, -1, :] / temperature
            probs = torch.softmax(next_token_logits, dim=-1)
            next_token_id = torch.multinomial(probs, num_samples=1)
            
            # Stop if EOS token
            if next_token_id.item() == tokenizer.eos_token_id:
                break
                
            # Append to sequence
            generated_ids = torch.cat([generated_ids, next_token_id.unsqueeze(0)], dim=1)
    
    # Decode generated text
    generated_text = tokenizer.decode(generated_ids[0], skip_special_tokens=True)
    return generated_text

def test_generation_quality():
    """Test the model's text generation quality."""
    
    # Try to load the fixed model
    model_path = "./fastllama_fixed_output/fixed_model"
    
    if not Path(model_path).exists():
        print(f"❌ Model not found at {model_path}")
        print("💡 Please run fix_training_issues.py first to train the model")
        return
    
    model, tokenizer, config = load_model(model_path)
    
    if model is None:
        print("❌ Failed to load model")
        return
    
    # Test prompts
    test_prompts = [
        "The future of artificial intelligence is",
        "In a world where technology advances rapidly,",
        "The most important lesson I learned was",
        "Once upon a time in a distant galaxy,",
        "The key to solving climate change lies in"
    ]
    
    print("\n🧪 Testing text generation quality...")
    print("=" * 60)
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n--- Test {i} ---")
        print(f"Prompt: {prompt}")
        
        try:
            generated_text = generate_text(
                model, tokenizer, prompt, 
                max_new_tokens=30, 
                temperature=0.7
            )
            
            # Extract only the generated part
            generated_part = generated_text[len(prompt):].strip()
            
            print(f"Generated: {generated_part}")
            
            # Quality checks
            if len(generated_part.split()) > 5:
                print("✅ Generated reasonable length text")
            else:
                print("⚠️ Generated text is quite short")
                
            if not any(char in generated_part for char in ['�', '▁', '##', 'П', 'wiggle']):
                print("✅ No obvious artifacts or gibberish")
            else:
                print("❌ Contains artifacts or gibberish")
                
        except Exception as e:
            print(f"❌ Generation failed: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Text generation test completed!")
    
    # Model info
    num_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"\n📊 Model Information:")
    print(f"Total parameters: {num_params:,} ({num_params/1e6:.1f}M)")
    print(f"Trainable parameters: {trainable_params:,} ({trainable_params/1e6:.1f}M)")
    print(f"Trainable ratio: {trainable_params/num_params*100:.1f}%")
    print(f"Hidden size: {config.hidden_size}")
    print(f"Layers: {config.num_hidden_layers}")
    print(f"Attention heads: {config.num_attention_heads}")

if __name__ == "__main__":
    print("🚀 FastLLaMA Text Generation Test")
    print("Testing the quality of the fixed FastLLaMA model...")
    
    test_generation_quality()
