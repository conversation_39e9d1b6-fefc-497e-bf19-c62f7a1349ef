"""
Setup script for FastLLaMA package.
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), "README.md")
    if os.path.exists(readme_path):
        with open(readme_path, "r", encoding="utf-8") as f:
            return f.read()
    return "FastLLaMA: Optimized LLaMA Architecture for Production"

# Read requirements
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), "requirements.txt")
    if os.path.exists(requirements_path):
        with open(requirements_path, "r", encoding="utf-8") as f:
            return [line.strip() for line in f if line.strip() and not line.startswith("#")]
    return []

setup(
    name="fastllama",
    version="1.0.0",
    author="FastLLaMA Team",
    author_email="<EMAIL>",
    description="Optimized LLaMA Architecture for Production",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/fastllama/fastllama",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: Apache Software License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.3.0",
            "black>=23.3.0",
            "flake8>=6.0.0",
            "mypy>=1.3.0",
        ],
        "all": [
            "pytest>=7.3.0",
            "black>=23.3.0",
            "flake8>=6.0.0",
            "mypy>=1.3.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "fastllama-train=examples.train_example:main",
            "fastllama-inference=examples.inference_example:main",
            "fastllama-benchmark=examples.benchmark:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
    keywords=[
        "llama",
        "transformer",
        "language-model",
        "deep-learning",
        "pytorch",
        "attention",
        "optimization",
        "inference",
        "training",
    ],
    project_urls={
        "Bug Reports": "https://github.com/fastllama/fastllama/issues",
        "Source": "https://github.com/fastllama/fastllama",
        "Documentation": "https://fastllama.readthedocs.io/",
    },
)
