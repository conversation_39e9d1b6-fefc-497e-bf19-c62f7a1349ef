2025-05-31 07:02:00,792 - INFO - 🚀 Final FastLLaMA Training - Phase 3: Complete Implementation
2025-05-31 07:02:00,792 - INFO - Device: cuda
2025-05-31 07:02:00,937 - INFO - ✅ Tokenizer loaded. Vocab size: 64811
2025-05-31 07:02:00,937 - INFO - 🧠 Creating final FastLLaMA model...
2025-05-31 07:02:04,115 - INFO - ✅ Model created: 200.8M total parameters
2025-05-31 07:02:04,115 - INFO - ✅ Trainable parameters: 200.8M
2025-05-31 07:02:04,115 - INFO - 🔍 Final Implementation Validation
2025-05-31 07:02:04,115 - INFO - 1. Testing gradient flow...
2025-05-31 07:02:05,665 - INFO - ✅ Gradient coverage: 92/146 (63.0%)
2025-05-31 07:02:05,666 - WARNING - ⚠️ Parameters without gradients: 54
2025-05-31 07:02:05,666 - WARNING -     - layers.0.self_attn.k_quantizer
2025-05-31 07:02:05,666 - WARNING -     - layers.0.self_attn.v_quantizer
2025-05-31 07:02:05,666 - WARNING -     - layers.1.self_attn.k_quantizer
2025-05-31 07:02:05,666 - WARNING -     - layers.1.self_attn.v_quantizer
2025-05-31 07:02:05,666 - WARNING -     - layers.2.self_attn.k_quantizer
2025-05-31 07:02:05,666 - INFO - 2. Testing feature activation...
2025-05-31 07:02:05,686 - INFO - ✅ Early exit outputs: 3
2025-05-31 07:02:05,687 - INFO - ✅ Context compression: False
2025-05-31 07:02:05,687 - INFO - 3. Testing performance...
2025-05-31 07:02:05,854 - INFO - ✅ Average inference time: 16.7ms
2025-05-31 07:02:05,854 - ERROR - ❌ Validation failed! Cannot proceed with training.
