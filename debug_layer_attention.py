"""
Debug script to understand which layers use which attention mechanisms
and why some KV quantizers don't receive gradients.
"""

import os
import sys
import torch
import torch.nn as nn

# Add FastLLaMA to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastllama import FastLLaMAConfig, FastLLaMAModel

def debug_attention_mechanisms():
    """Debug which layers use which attention mechanisms."""
    print("🔍 Debugging Attention Mechanisms")
    print("=" * 50)
    
    # Create test config
    config = FastLLaMAConfig(
        hidden_size=256,
        intermediate_size=256*4,
        num_attention_heads=4,
        num_key_value_heads=2,
        num_hidden_layers=4,
        vocab_size=1000,
        max_position_embeddings=1024,

        # Enable ALL advanced features
        enable_context_compression=True,
        enable_early_exit=True,
        kv_cache_quantization=True,
        parameter_sharing=True,

        # Configure attention layers
        local_layers=[0, 1],
        sparse_layers=[2],
        hierarchical_layers=[3],
        full_attention_layers=[],

        # Early exit configuration
        early_exit_layers=[1, 3],
        confidence_threshold=0.7,
    )
    
    model = FastLLaMAModel(config)
    
    print(f"Model has {len(model.layers)} layers")
    print(f"Config: local_layers={config.local_layers}")
    print(f"Config: sparse_layers={config.sparse_layers}")
    print(f"Config: hierarchical_layers={config.hierarchical_layers}")
    print(f"Config: early_exit_layers={config.early_exit_layers}")
    print()
    
    for i, layer in enumerate(model.layers):
        print(f"Layer {i}:")
        
        # Check attention type
        attn_type = type(layer.self_attn).__name__
        print(f"  Attention type: {attn_type}")
        
        # Check if has KV quantizers
        has_k_quantizer = hasattr(layer.self_attn, 'k_quantizer') and layer.self_attn.k_quantizer is not None
        has_v_quantizer = hasattr(layer.self_attn, 'v_quantizer') and layer.self_attn.v_quantizer is not None
        print(f"  KV quantizers: k={has_k_quantizer}, v={has_v_quantizer}")
        
        # Check if has group adapter
        has_group_adapter = hasattr(layer.self_attn, 'group_adapter')
        print(f"  Group adapter: {has_group_adapter}")
        
        # Check if has early exit
        has_early_exit = hasattr(layer, 'early_exit_head') and layer.early_exit_head is not None
        print(f"  Early exit: {has_early_exit}")
        
        # Check if has attention weights (hierarchical)
        has_attention_weights = hasattr(layer.self_attn, 'attention_weights')
        print(f"  Attention weights: {has_attention_weights}")
        
        print()

def test_gradient_flow_by_layer():
    """Test gradient flow for each layer individually."""
    print("🧪 Testing Gradient Flow by Layer")
    print("=" * 50)
    
    config = FastLLaMAConfig(
        hidden_size=256,
        num_attention_heads=4,
        num_key_value_heads=2,
        num_hidden_layers=4,
        vocab_size=1000,
        kv_cache_quantization=True,
        local_layers=[0, 1],
        sparse_layers=[2],
        hierarchical_layers=[3],
        early_exit_layers=[1, 3],
    )
    
    model = FastLLaMAModel(config)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)
    
    # Create test input
    batch_size = 2
    seq_length = 512
    input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_length)).to(device)
    labels = input_ids.clone()
    
    model.train()
    model.zero_grad()
    
    # Forward pass
    outputs = model(input_ids=input_ids, labels=labels, enable_early_exit=True)
    loss = outputs["loss"]
    
    # Backward pass
    loss.backward()
    
    # Check gradients by layer
    for i, layer in enumerate(model.layers):
        print(f"Layer {i} ({type(layer.self_attn).__name__}):")
        
        # Check KV quantizer gradients
        if hasattr(layer.self_attn, 'k_quantizer') and layer.self_attn.k_quantizer is not None:
            k_grad = layer.self_attn.k_quantizer.grad is not None
            v_grad = layer.self_attn.v_quantizer.grad is not None
            print(f"  KV quantizer gradients: k={k_grad}, v={v_grad}")
            
            if not k_grad:
                print(f"    k_quantizer value: {layer.self_attn.k_quantizer.item():.6f}")
            if not v_grad:
                print(f"    v_quantizer value: {layer.self_attn.v_quantizer.item():.6f}")
        
        # Check group adapter gradients
        if hasattr(layer.self_attn, 'group_adapter'):
            weight_grad = layer.self_attn.group_adapter.weight.grad is not None
            bias_grad = layer.self_attn.group_adapter.bias.grad is not None
            print(f"  Group adapter gradients: weight={weight_grad}, bias={bias_grad}")
        
        # Check early exit gradients
        if hasattr(layer, 'early_exit_head') and layer.early_exit_head is not None:
            exit_grad = layer.early_exit_head.weight.grad is not None
            conf_grad = layer.confidence_head.weight.grad is not None
            print(f"  Early exit gradients: exit_head={exit_grad}, confidence={conf_grad}")
        
        print()

def analyze_quantizer_usage():
    """Analyze when and how KV quantizers are used."""
    print("🔬 Analyzing KV Quantizer Usage")
    print("=" * 50)
    
    config = FastLLaMAConfig(
        hidden_size=256,
        num_attention_heads=4,
        num_key_value_heads=2,
        num_hidden_layers=4,
        vocab_size=1000,
        kv_cache_quantization=True,
        local_layers=[0, 1],
        sparse_layers=[2],
        hierarchical_layers=[3],
    )
    
    model = FastLLaMAModel(config)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)
    
    # Hook to track quantizer usage
    quantizer_calls = {}
    
    def create_quantizer_hook(layer_idx, quantizer_type):
        def hook(module, input, output):
            key = f"layer_{layer_idx}_{quantizer_type}"
            if key not in quantizer_calls:
                quantizer_calls[key] = 0
            quantizer_calls[key] += 1
        return hook
    
    # Register hooks
    for i, layer in enumerate(model.layers):
        if hasattr(layer.self_attn, '_quantize_kv'):
            # This is tricky - we need to hook the _quantize_kv method
            original_quantize = layer.self_attn._quantize_kv
            
            def make_wrapped_quantize(layer_idx):
                def wrapped_quantize(states, quantizer):
                    key = f"layer_{layer_idx}_quantize_call"
                    if key not in quantizer_calls:
                        quantizer_calls[key] = 0
                    quantizer_calls[key] += 1
                    return original_quantize(states, quantizer)
                return wrapped_quantize
            
            layer.self_attn._quantize_kv = make_wrapped_quantize(i)
    
    # Test with different inputs
    test_cases = [
        ("Short sequence", 128),
        ("Medium sequence", 512),
        ("Long sequence", 1024),
    ]
    
    for test_name, seq_len in test_cases:
        print(f"\n{test_name} ({seq_len} tokens):")
        quantizer_calls.clear()
        
        input_ids = torch.randint(0, config.vocab_size, (1, seq_len)).to(device)
        
        model.eval()
        with torch.no_grad():
            outputs = model(input_ids=input_ids)
        
        print(f"  Quantizer calls: {quantizer_calls}")

if __name__ == "__main__":
    debug_attention_mechanisms()
    print("\n" + "="*70 + "\n")
    test_gradient_flow_by_layer()
    print("\n" + "="*70 + "\n")
    analyze_quantizer_usage()
