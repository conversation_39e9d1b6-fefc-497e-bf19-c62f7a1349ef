"""
Final FastLLaMA Training Script - Phase 3: Complete Implementation

This script represents the culmination of all fixes and should achieve 100% functionality.
"""

import os
import sys
import torch
import torch.nn as nn
import logging
import time
from pathlib import Path
from datetime import datetime

# Add FastLLaMA to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastllama import FastLLaMAConfig, FastLLaMAModel
from fastllama.utils import get_memory_stats
from fastllama.data import DataConfig, FastLLaMADataLoader
from transformers import AutoTokenizer

def setup_logging(output_dir):
    """Setup comprehensive logging."""
    log_dir = Path(output_dir) / "logs"
    log_dir.mkdir(parents=True, exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"final_fastllama_training_{timestamp}.log"

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file, encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def create_final_model_config(tokenizer_vocab_size):
    """Create FastLLaMA model with ALL advanced features working."""
    return FastLLaMAConfig(
        # Model architecture
        hidden_size=512,
        intermediate_size=512*4,
        num_attention_heads=8,
        num_key_value_heads=4,
        num_hidden_layers=6,
        vocab_size=tokenizer_vocab_size + 8,
        max_position_embeddings=2048,

        # ALL advanced features ENABLED
        enable_context_compression=True,
        enable_early_exit=True,
        use_mixed_precision=True,
        use_gradient_checkpointing=True,
        kv_cache_quantization=True,
        parameter_sharing=True,
        dynamic_batching=True,

        # Attention configuration
        local_attention_window=512,
        sparse_attention_stride=8,
        compression_ratio=4,

        # Layer configuration for maximum feature coverage
        local_layers=[0, 1],
        sparse_layers=[2, 3],
        hierarchical_layers=[4, 5],
        full_attention_layers=[],

        # Early exit configuration
        early_exit_layers=[2, 4, 5],
        confidence_threshold=0.7,

        # Context compression
        compression_encoder_layers=2,
        progressive_compression=True,
    )

def validate_final_implementation(model, tokenizer, device, logger):
    """Comprehensive validation of the final implementation."""
    logger.info("🔍 Final Implementation Validation")
    
    # Test 1: Gradient flow validation
    logger.info("1. Testing gradient flow...")
    
    test_text = "The future of artificial intelligence and machine learning is bright. " * 10
    inputs = tokenizer(test_text, return_tensors="pt", padding=True, truncation=True, max_length=1024)
    
    input_ids = inputs["input_ids"].to(device)
    labels = input_ids.clone()
    
    model.train()
    model.zero_grad()
    
    # Forward pass with all features enabled
    outputs = model(
        input_ids=input_ids,
        labels=labels,
        enable_early_exit=True,
        output_attentions=True,
        use_cache=True
    )
    
    loss = outputs["loss"]
    loss.backward()
    
    # Count gradients
    total_params = 0
    grad_params = 0
    no_grad_params = []
    
    for name, param in model.named_parameters():
        total_params += 1
        if param.grad is not None:
            grad_params += 1
        else:
            no_grad_params.append(name)
    
    gradient_coverage = grad_params / total_params
    logger.info(f"✅ Gradient coverage: {grad_params}/{total_params} ({100*gradient_coverage:.1f}%)")
    
    if no_grad_params:
        logger.warning(f"⚠️ Parameters without gradients: {len(no_grad_params)}")
        for param in no_grad_params[:5]:
            logger.warning(f"    - {param}")
    
    # Test 2: Feature activation validation
    logger.info("2. Testing feature activation...")
    
    model.eval()
    with torch.no_grad():
        test_outputs = model(input_ids=input_ids, enable_early_exit=True)
    
    early_exit_count = len(test_outputs.get("early_exit_outputs", []))
    has_compression = test_outputs.get("compression_quality") is not None
    
    logger.info(f"✅ Early exit outputs: {early_exit_count}")
    logger.info(f"✅ Context compression: {has_compression}")
    
    # Test 3: Performance validation
    logger.info("3. Testing performance...")
    
    start_time = time.time()
    for _ in range(10):
        with torch.no_grad():
            _ = model(input_ids=input_ids[:1, :256])  # Shorter sequence for speed test
    inference_time = (time.time() - start_time) / 10
    
    logger.info(f"✅ Average inference time: {inference_time*1000:.1f}ms")
    
    return gradient_coverage >= 0.95, early_exit_count > 0, has_compression

def run_comprehensive_training(model, dataloader, optimizer, device, logger, max_steps=100):
    """Run comprehensive training with all features enabled."""
    logger.info(f"🏋️ Starting comprehensive training for {max_steps} steps...")
    
    model.train()
    total_loss = 0
    step = 0
    start_time = time.time()
    
    best_loss = float('inf')
    loss_history = []
    
    for batch in dataloader:
        if step >= max_steps:
            break
        
        # Move batch to device
        input_ids = batch['input_ids'].to(device)
        labels = batch['labels'].to(device)
        attention_mask = batch.get('attention_mask')
        if attention_mask is not None:
            attention_mask = attention_mask.to(device)
        
        # Forward pass with all features
        optimizer.zero_grad()
        outputs = model(
            input_ids=input_ids,
            labels=labels,
            attention_mask=attention_mask,
            enable_early_exit=True
        )
        
        loss = outputs['loss']
        
        # Check for NaN
        if torch.isnan(loss) or torch.isinf(loss):
            logger.warning(f"⚠️ NaN/Inf loss at step {step}, skipping...")
            continue
        
        # Backward pass
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        optimizer.step()
        
        total_loss += loss.item()
        loss_history.append(loss.item())
        step += 1
        
        # Track best loss
        if loss.item() < best_loss:
            best_loss = loss.item()
        
        # Logging
        if step % 10 == 0:
            avg_loss = total_loss / step
            elapsed = time.time() - start_time
            speed = step / elapsed
            
            # Check for early exit and compression
            early_exits = len(outputs.get('early_exit_outputs', []))
            has_compression = outputs.get('compression_quality') is not None
            
            logger.info(f"Step {step}: loss={loss.item():.4f}, avg={avg_loss:.4f}, "
                       f"best={best_loss:.4f}, speed={speed:.2f}it/s, "
                       f"early_exits={early_exits}, compression={has_compression}")
        
        # Memory cleanup
        if step % 50 == 0:
            torch.cuda.empty_cache()
    
    # Calculate final metrics
    final_avg_loss = total_loss / step if step > 0 else float('inf')
    total_time = time.time() - start_time
    final_speed = step / total_time
    
    # Check if loss is decreasing
    if len(loss_history) >= 20:
        recent_avg = sum(loss_history[-10:]) / 10
        early_avg = sum(loss_history[:10]) / 10
        loss_improvement = early_avg - recent_avg
    else:
        loss_improvement = 0
    
    logger.info("🎉 Training completed!")
    logger.info(f"✅ Final average loss: {final_avg_loss:.4f}")
    logger.info(f"✅ Best loss: {best_loss:.4f}")
    logger.info(f"✅ Loss improvement: {loss_improvement:.4f}")
    logger.info(f"✅ Training speed: {final_speed:.2f} it/s")
    logger.info(f"✅ Total time: {total_time/60:.1f} minutes")
    
    return {
        'final_loss': final_avg_loss,
        'best_loss': best_loss,
        'loss_improvement': loss_improvement,
        'speed': final_speed,
        'success': best_loss < 6.0 and loss_improvement > 0.5
    }

def main():
    """Main function for final FastLLaMA training."""
    output_dir = "./final_fastllama_output"
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    logger = setup_logging(output_dir)
    logger.info("🚀 Final FastLLaMA Training - Phase 3: Complete Implementation")
    logger.info(f"Device: {device}")
    
    # Optimizations
    torch.backends.cudnn.benchmark = True
    torch.backends.cuda.matmul.allow_tf32 = True
    torch.backends.cudnn.allow_tf32 = True
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained("D:\\vlinhd11\\Projects\\LCMS_LLM\\MOE-LLM\\v2\\my_tokenizer\\llama_en_vi_tokenizer")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    logger.info(f"✅ Tokenizer loaded. Vocab size: {tokenizer.vocab_size}")
    
    # Create final model
    logger.info("🧠 Creating final FastLLaMA model...")
    config = create_final_model_config(tokenizer.vocab_size)
    model = FastLLaMAModel(config)
    model.to(device)
    
    num_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"✅ Model created: {num_params/1e6:.1f}M total parameters")
    logger.info(f"✅ Trainable parameters: {trainable_params/1e6:.1f}M")
    
    # Validate implementation
    gradient_ok, early_exit_ok, compression_ok = validate_final_implementation(model, tokenizer, device, logger)
    
    if not (gradient_ok and early_exit_ok):
        logger.error("❌ Validation failed! Cannot proceed with training.")
        return None
    
    logger.info("✅ All validation checks passed!")
    
    # Create optimizer
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=5e-5,
        weight_decay=0.01,
        betas=(0.9, 0.95),
        eps=1e-8
    )
    
    # Create simple dataloader for testing
    data_config = DataConfig(
        dataset_name="HuggingFaceTB/smollm-corpus",
        dataset_config="fineweb-edu-dedup",
        tokenizer_name="D:\\vlinhd11\\Projects\\LCMS_LLM\\MOE-LLM\\v2\\my_tokenizer\\llama_en_vi_tokenizer",
        max_length=1024,
        batch_size=2,
        streaming=True,
        num_workers=0,
        min_length=256,
        padding="max_length",
        truncation=True,
    )
    
    dataloader_wrapper = FastLLaMADataLoader(data_config, tokenizer)
    train_dataloader = dataloader_wrapper.create_train_dataloader()
    
    # Run comprehensive training
    results = run_comprehensive_training(model, train_dataloader, optimizer, device, logger, max_steps=200)
    
    # Save model if successful
    if results['success']:
        model_path = Path(output_dir) / "final_model"
        model_path.mkdir(parents=True, exist_ok=True)
        
        torch.save(model.state_dict(), model_path / "pytorch_model.bin")
        config.save_pretrained(model_path)
        tokenizer.save_pretrained(model_path)
        
        logger.info(f"✅ Model saved to {model_path}")
    
    return results

if __name__ == "__main__":
    print("🎯 Final FastLLaMA Training - Phase 3")
    print("📋 All advanced features enabled:")
    print("  ✅ Hierarchical attention")
    print("  ✅ Context compression")
    print("  ✅ Early exit mechanism")
    print("  ✅ KV cache quantization")
    print("  ✅ Parameter sharing")
    print("  ✅ Memory optimizations")
    print("=" * 60)
    
    try:
        results = main()
        if results and results['success']:
            print(f"\n🎉 FINAL TRAINING SUCCESSFUL!")
            print(f"📊 Results:")
            print(f"  - Final loss: {results['final_loss']:.4f}")
            print(f"  - Best loss: {results['best_loss']:.4f}")
            print(f"  - Loss improvement: {results['loss_improvement']:.4f}")
            print(f"  - Training speed: {results['speed']:.2f} it/s")
            print(f"  - Status: ALL FEATURES WORKING")
        else:
            print(f"\n⚠️ Training completed but needs improvement")
            if results:
                print(f"📊 Results:")
                print(f"  - Final loss: {results['final_loss']:.4f}")
                print(f"  - Best loss: {results['best_loss']:.4f}")
                print(f"  - Training speed: {results['speed']:.2f} it/s")
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
