2025-05-31 06:55:04,238 - INFO - 🚀 Fixed FastLLaMA Training - Phase 1: Basic Training with Advanced Features
2025-05-31 06:55:04,239 - INFO - Device: cuda
2025-05-31 06:55:04,417 - INFO - ✅ Tokenizer loaded. Vocab size: 64811
2025-05-31 06:55:04,417 - INFO - 🧠 Creating FastLLaMA model with FIXED advanced features...
2025-05-31 06:55:07,745 - INFO - ✅ Model created: 208.7M total parameters
2025-05-31 06:55:07,745 - INFO - ✅ Trainable parameters: 208.7M (100.0%)
2025-05-31 06:55:07,746 - INFO - 🔍 Validating gradient flow...
2025-05-31 06:55:07,746 - INFO - ✅ Parameters requiring gradients: 172/172
2025-05-31 06:55:07,746 - INFO - 🔍 Checking advanced features...
2025-05-31 06:55:07,747 - INFO - ✅ Hierarchical attention layers: 2
2025-05-31 06:55:07,747 - INFO - ✅ Attention weights parameters: 6
2025-05-31 06:55:07,747 - INFO - ✅ Context compression enabled: True
2025-05-31 06:55:07,747 - INFO - ✅ Early exit heads: 3
2025-05-31 06:55:07,748 - INFO - ✅ KV quantizers: 6
2025-05-31 06:55:07,748 - INFO - 🧪 Testing forward pass with gradients...
2025-05-31 06:55:09,619 - INFO - ✅ Parameters with gradients: 114/172
2025-05-31 06:55:09,621 - INFO - ✅ Forward pass successful. Total Loss: 14.4244
2025-05-31 06:55:09,621 - WARNING - ⚠️ Parameters without gradients: 58
2025-05-31 06:55:09,622 - WARNING -     - layers.0.self_attn.k_quantizer
2025-05-31 06:55:09,622 - WARNING -     - layers.0.self_attn.v_quantizer
2025-05-31 06:55:09,622 - WARNING -     - layers.1.self_attn.k_quantizer
2025-05-31 06:55:09,622 - WARNING -     - layers.1.self_attn.v_quantizer
2025-05-31 06:55:09,622 - WARNING -     - layers.2.self_attn.k_quantizer
2025-05-31 06:55:09,622 - WARNING -     ... and 53 more
2025-05-31 06:55:09,623 - ERROR - ❌ Forward pass test failed!
